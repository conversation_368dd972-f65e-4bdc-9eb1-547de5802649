import mongoose, { Document, Schema } from 'mongoose';

export interface IOrderItem {
  productId: string;
  productName: string;
  productImage: string;
  price: number;
  quantity: number;
  selectedOptions: {
    size: string;
    color: string;
    additions: Array<{ id: string; name: string; price: number }>;
    without: string[];
    sides: Array<{ id: string; name: string; price: number }>;
  };
  subtotal: number;
}

export interface IOrder extends Document {
  orderId: string;
  userId: string;
  supplierId: string;
  supplierName: string;
  items: IOrderItem[];
  subtotal: number;
  deliveryFee: number;
  totalAmount: number;
  promoCode?: string;
  promoDiscount: number;
  status: 'pending' | 'confirmed' | 'preparing' | 'ready' | 'out_for_delivery' | 'delivered' | 'cancelled';
  paymentMethod: 'cash' | 'card' | 'wallet';
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  deliveryAddress: {
    street: string;
    city: string;
    coordinates: {
      lat: number;
      lng: number;
    };
    notes?: string;
  };
  customerPhone: string;
  estimatedDeliveryTime?: string;
  actualDeliveryTime?: string;
  driverId?: string;
  driverName?: string;
  driverPhone?: string;
  trackingUpdates: Array<{
    status: string;
    timestamp: Date;
    message: string;
    location?: {
      lat: number;
      lng: number;
    };
  }>;
  notes?: string;
  rating?: {
    score: number;
    comment?: string;
    ratedAt: Date;
  };
  createdAt: Date;
  updatedAt: Date;
}

const OrderItemSchema: Schema = new Schema({
  productId: { type: String, required: true },
  productName: { type: String, required: true, trim: true },
  productImage: { type: String, required: false },
  price: { type: Number, required: true, min: 0 },
  quantity: { type: Number, required: true, min: 1 },
  selectedOptions: {
    type: {
      size: { type: String, default: '' },
      color: { type: String, default: '' },
      additions: {
        type: [{
          id: { type: String, required: true },
          name: { type: String, required: true },
          price: { type: Number, required: true, min: 0 }
        }],
        default: []
      },
      without: { type: [String], default: [] },
      sides: {
        type: [{
          id: { type: String, required: true },
          name: { type: String, required: true },
          price: { type: Number, required: true, min: 0 }
        }],
        default: []
      }
    },
    required: true,
    default: {
      size: '',
      color: '',
      additions: [],
      without: [],
      sides: []
    }
  },
  subtotal: { type: Number, required: true, min: 0 }
});

const OrderSchema: Schema = new Schema({
  orderId: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  userId: {
    type: String,
    required: true,
    trim: true
  },
  supplierId: {
    type: String,
    required: true,
    trim: true
  },
  supplierName: {
    type: String,
    required: true,
    trim: true
  },
  items: [OrderItemSchema],
  subtotal: {
    type: Number,
    required: true,
    min: 0
  },
  deliveryFee: {
    type: Number,
    required: true,
    min: 0
  },
  totalAmount: {
    type: Number,
    required: true,
    min: 0
  },
  promoCode: {
    type: String,
    trim: true
  },
  promoDiscount: {
    type: Number,
    default: 0,
    min: 0
  },
  status: {
    type: String,
    enum: ['pending', 'confirmed', 'preparing', 'ready', 'out_for_delivery', 'delivered', 'cancelled'],
    default: 'pending'
  },
  paymentMethod: {
    type: String,
    enum: ['cash', 'card', 'wallet'],
    required: true
  },
  paymentStatus: {
    type: String,
    enum: ['pending', 'paid', 'failed', 'refunded'],
    default: 'pending'
  },
  deliveryAddress: {
    street: { type: String, required: true, trim: true },
    city: { type: String, required: true, trim: true },
    coordinates: {
      lat: { type: Number, required: true },
      lng: { type: Number, required: true }
    },
    notes: { type: String, trim: true }
  },
  customerPhone: {
    type: String,
    required: true,
    trim: true
  },
  estimatedDeliveryTime: {
    type: String,
    trim: true
  },
  actualDeliveryTime: {
    type: String,
    trim: true
  },
  driverId: {
    type: String,
    trim: true
  },
  driverName: {
    type: String,
    trim: true
  },
  driverPhone: {
    type: String,
    trim: true
  },
  trackingUpdates: [{
    status: { type: String, required: true },
    timestamp: { type: Date, required: true },
    message: { type: String, required: true, trim: true },
    location: {
      lat: { type: Number },
      lng: { type: Number }
    }
  }],
  notes: {
    type: String,
    trim: true
  },
  rating: {
    score: { type: Number, min: 1, max: 5 },
    comment: { type: String, trim: true },
    ratedAt: { type: Date }
  }
}, {
  timestamps: true
});

// Indexes for efficient queries
OrderSchema.index({ userId: 1, createdAt: -1 });
OrderSchema.index({ supplierId: 1, createdAt: -1 });
OrderSchema.index({ status: 1, createdAt: -1 });
// Note: orderId index is automatically created by unique: true

export const Order = mongoose.model<IOrder>('Order', OrderSchema);
